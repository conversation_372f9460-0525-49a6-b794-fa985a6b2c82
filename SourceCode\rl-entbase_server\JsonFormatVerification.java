import com.alibaba.fastjson.JSONObject;
import java.util.ArrayList;
import java.util.List;

/**
 * Simple JSON format verification program
 * Verify convertJsonObjectToSqlTemplate method can handle new JSON format correctly
 */
public class JsonFormatVerification {

    public static void main(String[] args) {
        System.out.println("Starting JSON format verification...");

        // Test new format
        testNewJsonFormat();

        // Test old format compatibility
        testOldJsonFormat();

        System.out.println("Verification completed!");
    }
    
    private static void testNewJsonFormat() {
        System.out.println("\n=== Testing New JSON Format ===");

        // Simulate new JSON format
        String jsonStr = "{\"SQL_TEMPLATE\":{\"code\":\"SQL_TEMPLATE\",\"sqlTemplate\":{\"paramNames\":[],\"renderedSql\":\"SELECT A.CODE, A.NAME FROM CRM_CUSTOMER A\",\"originalSql\":\"SELECT A.CODE, <PERSON>.NAME FROM CRM_CUSTOMER A\",\"uniqueParamNames\":[]},\"name\":\"SQL_TEMPLATE\"}}";

        try {
            JSONObject jsonObj = JSONObject.parseObject(jsonStr);
            System.out.println("Parsed JSON object: " + jsonObj);

            // Test new format parsing
            MockSqlTemplate sqlTemplate = convertJsonObjectToSqlTemplate(jsonObj);

            if (sqlTemplate != null) {
                System.out.println("SUCCESS: Converted to SqlTemplate");
                System.out.println("  Original SQL: " + sqlTemplate.originalSql);
                System.out.println("  Rendered SQL: " + sqlTemplate.renderedSql);
                System.out.println("  Param count: " + sqlTemplate.paramNames.size());
            } else {
                System.out.println("FAILED: Conversion failed");
            }
        } catch (Exception e) {
            System.out.println("FAILED: Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testOldJsonFormat() {
        System.out.println("\n=== Testing Old JSON Format Compatibility ===");

        // Simulate old JSON format
        String jsonStr = "{\"paramNames\":[],\"renderedSql\":\"SELECT A.CODE, A.NAME FROM CRM_CUSTOMER A\",\"originalSql\":\"SELECT A.CODE, A.NAME FROM CRM_CUSTOMER A\",\"uniqueParamNames\":[]}";

        try {
            JSONObject jsonObj = JSONObject.parseObject(jsonStr);
            System.out.println("Parsed JSON object: " + jsonObj);

            // Test old format parsing
            MockSqlTemplate sqlTemplate = convertJsonObjectToSqlTemplate(jsonObj);

            if (sqlTemplate != null) {
                System.out.println("SUCCESS: Converted to SqlTemplate");
                System.out.println("  Original SQL: " + sqlTemplate.originalSql);
                System.out.println("  Rendered SQL: " + sqlTemplate.renderedSql);
                System.out.println("  Param count: " + sqlTemplate.paramNames.size());
            } else {
                System.out.println("FAILED: Conversion failed");
            }
        } catch (Exception e) {
            System.out.println("FAILED: Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Convert JSONObject to SqlTemplate (copied logic from DictFieldUtilsV3)
     */
    private static MockSqlTemplate convertJsonObjectToSqlTemplate(JSONObject jsonObj) {
        try {
            // Check if it's the new nested format
            if (jsonObj.containsKey("SQL_TEMPLATE")) {
                JSONObject sqlTemplateWrapper = jsonObj.getJSONObject("SQL_TEMPLATE");
                if (sqlTemplateWrapper != null && sqlTemplateWrapper.containsKey("sqlTemplate")) {
                    JSONObject sqlTemplateObj = sqlTemplateWrapper.getJSONObject("sqlTemplate");
                    if (sqlTemplateObj != null) {
                        return extractSqlTemplateFromJson(sqlTemplateObj);
                    }
                }
            }

            // Compatible with old format: extract directly from root level
            return extractSqlTemplateFromJson(jsonObj);
        } catch (Exception e) {
            System.out.println("Failed to convert JSONObject to SqlTemplate: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * Extract SqlTemplate information from JSON object
     */
    private static MockSqlTemplate extractSqlTemplateFromJson(JSONObject jsonObj) {
        try {
            String originalSql = jsonObj.getString("originalSql");
            String renderedSql = jsonObj.getString("renderedSql");
            com.alibaba.fastjson.JSONArray paramNamesArray = jsonObj.getJSONArray("paramNames");

            if (originalSql != null) {
                // If there's original SQL, use it to create SqlTemplate
                List<String> paramNames = new ArrayList<>();
                if (paramNamesArray != null) {
                    for (int i = 0; i < paramNamesArray.size(); i++) {
                        paramNames.add(paramNamesArray.getString(i));
                    }
                }
                return new MockSqlTemplate(originalSql, renderedSql != null ? renderedSql : originalSql, paramNames);
            } else if (renderedSql != null && paramNamesArray != null) {
                // If no original SQL but has rendered SQL and parameter list
                List<String> paramNames = new ArrayList<>();
                for (int i = 0; i < paramNamesArray.size(); i++) {
                    paramNames.add(paramNamesArray.getString(i));
                }
                return new MockSqlTemplate(renderedSql, renderedSql, paramNames);
            }
        } catch (Exception e) {
            System.out.println("Failed to extract SqlTemplate info from JSON: " + e.getMessage());
        }
        return null;
    }

    /**
     * Simple SqlTemplate mock class
     */
    static class MockSqlTemplate {
        String originalSql;
        String renderedSql;
        List<String> paramNames;

        public MockSqlTemplate(String originalSql, String renderedSql, List<String> paramNames) {
            this.originalSql = originalSql;
            this.renderedSql = renderedSql;
            this.paramNames = paramNames != null ? paramNames : new ArrayList<>();
        }
    }
}
